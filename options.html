<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title></title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="container">
        <div id="globalStatusMessage" class="global-status-message"></div>
        <div class="top-buttons">
            <div class="global-switch-container top-right-switch">
                <label class="switch">
                    <input type="checkbox" id="globalEnableSwitch">
                    <span class="slider round"></span>
                </label>
            </div>
        </div>
        <div class="section">
            <h3>Target</h3>
            <input type="text" id="targetHostInput" placeholder="http://localhost:8080">
        </div>
        <div class="section">
            <h2>API Rules</h2>
            <div id="apiRulesContainer">
                <!-- 动态插入已有规则项，每项后面有删除按钮 -->
            </div>
            <div class="api-rule">
                <input type="text" id="newRuleInput" class="api-pattern" placeholder="URL Pattern (e.g. https://ipublish-dev.unipus.cn/**)">
                <button id="addRuleBtn" class="btn icon-btn">+</button>
            </div>
        </div>
        <p id="statusMessage" class="status"></p>
    </div>
    <script src="options.js"></script>
</body>
</html>