document.addEventListener('DOMContentLoaded', () => {
    const rulesList = document.getElementById('rulesList');
    const newRuleInput = document.getElementById('newRuleInput');
    const ruleCount = document.getElementById('ruleCount');
    const addBtn = document.querySelector('.add-btn');
    const proxySwitch = document.getElementById('proxySwitch');
    const targetHost = document.getElementById('targetHost');

    // 性能优化：缓存设置和防抖
    let settingsCache = null;
    let saveTimeout = null;

    // 防抖保存函数
    function debouncedSave(settings, callback) {
        if (saveTimeout) {
            clearTimeout(saveTimeout);
        }
        saveTimeout = setTimeout(() => {
            chrome.storage.local.set({ settings }, () => {
                settingsCache = settings;
                chrome.runtime.sendMessage({
                    type: 'SETTINGS_UPDATED',
                    payload: settings
                });
                if (callback) callback();
            });
        }, 100);
    }

    // 渲染规则列表，支持启用/停止/编辑/删除（优化版本）
    function renderRules(rules, forceRefresh = false) {
        // 使用缓存避免不必要的重新渲染
        if (!forceRefresh && settingsCache &&
            JSON.stringify(settingsCache.pathRules) === JSON.stringify(rules)) {
            return;
        }

        chrome.storage.local.get('settings', (data) => {
            const settings = data.settings || {};
            settingsCache = settings;
            let enabledList = Array.isArray(settings.pathRulesEnabled) ? settings.pathRulesEnabled : rules.map(() => true);
            while (enabledList.length < rules.length) enabledList.push(true);
            while (enabledList.length > rules.length) enabledList.pop();

            // 使用 DocumentFragment 优化DOM操作
            const fragment = document.createDocumentFragment();

            rules.forEach((rule, idx) => {
                const row = document.createElement('div');
                row.className = 'input-row';
                const enabled = enabledList[idx];

                // 创建规则内容元素
                const ruleContent = document.createElement('div');
                ruleContent.className = `rule-item-content ${enabled ? '' : 'disabled'}`;
                ruleContent.setAttribute('data-idx', idx);
                ruleContent.setAttribute('data-full-text', rule);
                ruleContent.title = enabled ? '点击禁用规则' : '点击启用规则';
                ruleContent.textContent = rule;

                // 创建操作按钮容器
                const actions = document.createElement('div');
                actions.className = 'rule-actions';

                const editBtn = document.createElement('button');
                editBtn.className = 'rule-btn edit-btn';
                editBtn.setAttribute('data-idx', idx);
                editBtn.title = '编辑规则';
                editBtn.textContent = '✎';

                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'rule-btn delete-btn';
                deleteBtn.setAttribute('data-idx', idx);
                deleteBtn.title = '删除规则';
                deleteBtn.textContent = '×';

                actions.appendChild(editBtn);
                actions.appendChild(deleteBtn);
                row.appendChild(ruleContent);
                row.appendChild(actions);
                fragment.appendChild(row);
            });

            // 一次性更新DOM
            rulesList.innerHTML = '';
            rulesList.appendChild(fragment);
            ruleCount.textContent = `${rules?.length || 0} 条规则`;

            // 绑定事件处理器
            bindRuleEvents(rules, enabledList);
        });
    }

    // 使用事件委托优化事件处理
    let eventDelegationSetup = false;

    function bindRuleEvents(rules, enabledList) {
        // 只设置一次事件委托
        if (!eventDelegationSetup) {
            rulesList.addEventListener('click', function(e) {
                const target = e.target;
                const idx = parseInt(target.getAttribute('data-idx'));

                if (isNaN(idx)) return;

                if (target.classList.contains('rule-item-content')) {
                    // 获取最新的规则和启用状态
                    chrome.storage.local.get('settings', (data) => {
                        const settings = data.settings || {};
                        const currentRules = settings.pathRules || [];
                        const currentEnabledList = settings.pathRulesEnabled || [];
                        toggleRuleEnabled(idx, currentRules, currentEnabledList);
                    });
                } else if (target.classList.contains('edit-btn')) {
                    e.stopPropagation();
                    chrome.storage.local.get('settings', (data) => {
                        const settings = data.settings || {};
                        const currentRules = settings.pathRules || [];
                        editRule(idx, currentRules);
                    });
                } else if (target.classList.contains('delete-btn')) {
                    e.stopPropagation();
                    chrome.storage.local.get('settings', (data) => {
                        const settings = data.settings || {};
                        const currentRules = settings.pathRules || [];
                        deleteRule(idx, currentRules);
                    });
                }
            });
            eventDelegationSetup = true;
        }
    }

    // 显示临时消息
    function showMessage(text, type = 'info', duration = 2000) {
        // 移除现有消息
        const existingMessage = document.querySelector('.temp-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        const message = document.createElement('div');
        message.className = `temp-message temp-message-${type}`;
        message.textContent = text;
        message.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
            color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(message);

        setTimeout(() => {
            if (message.parentNode) {
                message.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => message.remove(), 300);
            }
        }, duration);
    }

    // 切换规则启用状态（优化版本）
    function toggleRuleEnabled(idx, rules, enabledList) {
        enabledList[idx] = !enabledList[idx];

        chrome.storage.local.get('settings', (data) => {
            const settings = data.settings || {};
            settings.pathRulesEnabled = enabledList;

            // 使用防抖保存
            debouncedSave(settings, () => {
                renderRules(rules, true); // 强制刷新
                showMessage(
                    `规则已${enabledList[idx] ? '启用' : '禁用'}`,
                    'success',
                    1500
                );
            });
        });
    }

    // 编辑规则
    function editRule(idx, rules) {
        const currentRule = rules[idx];
        const newRule = prompt('编辑规则:', currentRule);

        if (newRule === null) {
            return; // 用户取消
        }

        const trimmedRule = newRule.trim();
        if (trimmedRule === '') {
            showMessage('规则不能为空', 'error');
            return;
        }

        if (trimmedRule === currentRule) {
            showMessage('规则未发生变化', 'info');
            return;
        }

        // 检查是否与其他规则重复
        if (rules.some((rule, i) => i !== idx && rule === trimmedRule)) {
            showMessage('该规则已存在', 'error');
            return;
        }

        chrome.storage.local.get('settings', (data) => {
            const settings = data.settings || {};
            settings.pathRules = settings.pathRules || [];
            settings.pathRules[idx] = trimmedRule;

            // 使用防抖保存
            debouncedSave(settings, () => {
                showMessage('规则已更新', 'success');
            });
        });
    }

    // 删除规则
    function deleteRule(idx, rules) {
        const ruleToDelete = rules[idx];
        const shortRule = ruleToDelete.length > 30 ? ruleToDelete.substring(0, 30) + '...' : ruleToDelete;

        if (confirm(`确定要删除规则 "${shortRule}" 吗？`)) {
            chrome.storage.local.get('settings', (data) => {
                const settings = data.settings || {};
                settings.pathRules = settings.pathRules || [];
                settings.pathRulesEnabled = settings.pathRulesEnabled || [];

                // 删除对应索引的规则和启用状态
                settings.pathRules.splice(idx, 1);
                settings.pathRulesEnabled.splice(idx, 1);

                // 使用防抖保存
                debouncedSave(settings, () => {
                    showMessage('规则已删除', 'success');
                });
            });
        }
    }

    // 加载规则
    function loadRules() {
        chrome.storage.local.get('settings', (data) => {
            const settings = data.settings || {};
            renderRules(settings.pathRules || []);
            // 同步主机和开关
            proxySwitch.checked = settings.globalEnable || false;
            targetHost.value = settings.targetHost || '';
        });
    }

    // 保存设置到storage（优化版本）
    function saveSettings() {
        chrome.storage.local.get('settings', (data) => {
            const settings = data.settings || {};
            settings.globalEnable = proxySwitch.checked;
            settings.targetHost = targetHost.value.trim();

            // 使用防抖保存
            debouncedSave(settings);
        });
    }

    // 添加规则
    function addRule() {
        const ruleText = newRuleInput.value.trim();
        if (!ruleText) {
            newRuleInput.focus();
            return;
        }

        chrome.storage.local.get('settings', (data) => {
            const settings = data.settings || {};
            let rules = settings.pathRules || [];
            let enabledList = Array.isArray(settings.pathRulesEnabled) ? settings.pathRulesEnabled : rules.map(() => true);

            // 检查是否已存在相同规则
            if (rules.includes(ruleText)) {
                alert('该规则已存在！');
                newRuleInput.focus();
                return;
            }

            rules.push(ruleText);
            enabledList.push(true);
            settings.pathRules = rules;
            settings.pathRulesEnabled = enabledList;

            // 使用防抖保存
            debouncedSave(settings, () => {
                newRuleInput.value = '';
                newRuleInput.focus();
            });
        });
    }

    // 添加规则按钮事件
    addBtn.addEventListener('click', addRule);

    // 输入框回车键添加规则
    newRuleInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            addRule();
        }
    });

    // 监听代理开关变化
    proxySwitch.addEventListener('change', saveSettings);

    // 监听目标主机输入变化
    targetHost.addEventListener('input', saveSettings);

    // 监听 storage 变化自动刷新
    chrome.storage.onChanged.addListener((changes) => {
        if (changes.settings) {
            const newSettings = changes.settings.newValue || {};
            renderRules(newSettings.pathRules || []);
            proxySwitch.checked = newSettings.globalEnable || false;
            targetHost.value = newSettings.targetHost || '';
        }
    });

    // 添加键盘快捷键支持
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + Enter 添加规则
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            if (document.activeElement === newRuleInput) {
                addRule();
            }
        }

        // Escape 键清空输入框
        if (e.key === 'Escape' && document.activeElement === newRuleInput) {
            newRuleInput.value = '';
            newRuleInput.blur();
        }
    });

    // 添加批量操作功能
    function addBulkOperations() {
        const ruleHeader = document.querySelector('.rule-header');
        if (ruleHeader && !ruleHeader.querySelector('.bulk-actions')) {
            const bulkActions = document.createElement('div');
            bulkActions.className = 'bulk-actions';
            bulkActions.innerHTML = `
                <button class="rule-btn" id="enableAllBtn" title="启用所有规则">全启</button>
                <button class="rule-btn" id="disableAllBtn" title="禁用所有规则">全禁</button>
                <button class="rule-btn delete-btn" id="clearAllBtn" title="清空所有规则">清空</button>
            `;
            ruleHeader.appendChild(bulkActions);

            // 绑定批量操作事件
            document.getElementById('enableAllBtn').addEventListener('click', () => {
                chrome.storage.local.get('settings', (data) => {
                    const settings = data.settings || {};
                    const rules = settings.pathRules || [];
                    settings.pathRulesEnabled = rules.map(() => true);
                    chrome.storage.local.set({ settings }, () => {
                        chrome.runtime.sendMessage({
                            type: 'SETTINGS_UPDATED',
                            payload: settings
                        });
                    });
                });
            });

            document.getElementById('disableAllBtn').addEventListener('click', () => {
                chrome.storage.local.get('settings', (data) => {
                    const settings = data.settings || {};
                    const rules = settings.pathRules || [];
                    settings.pathRulesEnabled = rules.map(() => false);
                    chrome.storage.local.set({ settings }, () => {
                        chrome.runtime.sendMessage({
                            type: 'SETTINGS_UPDATED',
                            payload: settings
                        });
                    });
                });
            });

            document.getElementById('clearAllBtn').addEventListener('click', () => {
                chrome.storage.local.get('settings', (data) => {
                    const settings = data.settings || {};
                    const ruleCount = (settings.pathRules || []).length;
                    if (ruleCount > 0 && confirm(`确定要清空所有 ${ruleCount} 条规则吗？此操作不可撤销！`)) {
                        settings.pathRules = [];
                        settings.pathRulesEnabled = [];
                        chrome.storage.local.set({ settings }, () => {
                            chrome.runtime.sendMessage({
                                type: 'SETTINGS_UPDATED',
                                payload: settings
                            });
                        });
                    }
                });
            });
        }
    }

    // 初始化
    loadRules();
    addBulkOperations();

    // 确保background.js已加载
    if (!chrome.runtime?.sendMessage) {
        console.error('无法连接到background.js');
        document.body.innerHTML = '<p style="color:red;padding:15px;">插件初始化失败，请刷新页面重试</p>';
    }

    addBtn.innerHTML = `<svg width="20" height="20" viewBox="0 0 20 20" fill="none"><path d="M10 4v12M4 10h12" stroke="white" stroke-width="2" stroke-linecap="round"/></svg>`;
});