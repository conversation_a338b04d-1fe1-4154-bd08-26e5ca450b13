项目名称：开发代理：API转发与认证缓存（增强版）

版本：2.1.0

描述：
这是一个极简Chrome浏览器插件，专为Web开发者设计。它可以拦截页面发出的API请求，根据用户设置的规则将其转发到指定目标主机，并自动从localStorage中提取Authorization信息附加到请求头，极大简化开发和调试流程。

---

文件结构：

- `manifest.json`：插件的核心配置文件，定义权限、脚本和基本信息。
- `options.html` / `options.css` / `options.js`：插件的设置页面，支持代理开关、目标主机、路径规则等配置。
- `background.js`：插件后台脚本，负责API请求拦截、转发和认证头处理。
- `content.js`：内容脚本，从 localStorage中提取Authorization并传递给后台。
- `interceptor.js`：拦截器脚本，在页面上下文中拦截fetch请求。
- `icons/`：插件图标文件夹。
- `proxy-log/`：辅助日志工具，可用于调试和监控代理请求。

---

如何加载和使用：

1.  **加载插件**：
    - 打开Chrome浏览器，访问 `chrome://extensions`。
    - 开启“开发者模式”。
    - 点击“加载已解压的扩展程序”，选择本项目文件夹。

2.  **配置插件**：
    - 在扩展管理页点击“详情”下的“扩展选项”进入设置页面。
    - 启用API代理，填写目标主机（如 `https://dev.api.example.com`）。
    - 设置URL路径匹配规则（每行一个前缀，留空表示全部代理）。
    - 可一键清除认证缓存。

3.  **使用说明**：
    - 设置完成后，所有匹配规则的API请求会自动转发到目标主机。
    - 自动提取并附加多种源的Authorization信息（localStorage、sessionStorage等）。
    - 实时显示代理状态和成功提示。
    - 插件全程后台运行，无弹窗，无多余干扰。

---

新增功能（v2.1.0）：

1. **完整的fetch拦截**: 现在可以拦截并代理所有fetch请求
2. **增强的Authorization提取**: 支持多种数据源和格式
3. **实时状态反馈**: 页面上显示代理状态和成功提示
4. **详细日志**: 控制台中显示详细的代理过程日志
5. **缓存管理**: 可手动清除认证缓存
6. **更好的错误处理**: 增强的异常处理和回退机制

---

如需反馈或贡献，请访问本项目仓库。