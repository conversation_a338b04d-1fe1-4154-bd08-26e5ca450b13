<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API-TOOL</title>
    <style>
        :root {
            --primary-color: #46ad24;
            --secondary-color: #554bd6;
            --success-color: #4cc9f0;
            --danger-color: #f44336;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --tab-height: 36px;
            --light-bg: #f5f7fa;
            --card-bg: #fff;
            --border: #e0e0e0;
            --shadow: 0 2px 8px rgba(0,0,0,0.06);
            --input-bg: #fff;
        }
        
        .header {
            position: relative;
        }
        
        .proxy-toggle {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
        }
        body {
            font-family: 'Segoe UI', system-ui, sans-serif;
            width: 300px;
            margin: 0;
            padding: 0;
            background: var(--light-bg);
            color: #222;
        }
        
        .container {
            padding: 8px 6px 6px 6px;
        }

        .header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .logo {
            width: 32px;
            height: 32px;
            margin-right: 8px;
        }

        .title {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            color: var(--primary-color);
        }

        .status-panel {
            margin-bottom: 10px;
        }
        
        .status-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
            padding: 4px 0;
        }
        
        .status-label {
            font-size: 14px;
            color: #6c757d;
        }
        
        .status-value {
            font-weight: 600;
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--primary-color);
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            text-align: center;
            margin: 12px 0;
            gap: 8px;
        }
        
        .btn:hover {
            background-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-options {
            background-color: white;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
            margin-top: 8px;
        }

        .btn-options:hover {
            background-color: rgba(67, 97, 238, 0.05);
        }
        
        .rule-list {
            padding: 8px;
            margin-top: 6px;
            background: #f8f9fa;
            border-radius: 8px;
            box-shadow: var(--shadow);
            border: 1px solid #e9ecef;
        }

        .rule-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            font-size: 12px;
            margin-bottom: 8px;
            letter-spacing: 0.3px;
            color: #6c757d;
            padding-bottom: 4px;
            border-bottom: 1px solid #e9ecef;
        }

        .rules-container {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .rules-list {
            max-height: 220px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 4px;
            margin-bottom: 6px;
            padding: 2px;
        }
        
        .input-row {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 2px;
            padding: 2px;
            border-radius: 6px;
            transition: background-color 0.2s;
        }

        .input-row:hover {
            background-color: rgba(67, 97, 238, 0.02);
        }

        .rule-item-content, .rule-input {
            flex: 1;
            padding: 8px 10px;
            border: 1px solid var(--border);
            border-radius: 6px;
            font-size: 13px;
            background: #fff;
            transition: all 0.2s ease;
            outline: none;
            box-sizing: border-box;
            height: 32px;
            display: flex;
            align-items: center;
            line-height: 1.2;
        }

        .rule-item-content {
            color: #333;
            cursor: pointer;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            user-select: none;
        }

        .rule-item-content.disabled {
            opacity: 0.7;
            background-color: #f8f9fa;
            color: #6c757d;
            border-style: dashed;
            position: relative;
        }

        .rule-item-content.disabled::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(108, 117, 125, 0.1) 2px,
                rgba(108, 117, 125, 0.1) 4px
            );
            border-radius: 6px;
            pointer-events: none;
        }

        .rule-item-content:hover::after {
            content: attr(data-full-text);
            position: absolute;
            top: 100%;
            left: 0;
            min-width: 200px;
            max-width: 300px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1000;
            white-space: nowrap;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            margin-top: 4px;
            animation: tooltipFadeIn 0.2s ease-out;
            border: 1px solid rgba(255,255,255,0.1);
        }

        @keyframes tooltipFadeIn {
            from {
                opacity: 0;
                transform: translateY(-5px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes buttonPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .rule-btn:active {
            animation: buttonPulse 0.1s ease-in-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .rule-item-content:focus, .rule-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1);
        }
        
        .rule-actions {
            display: flex;
            align-items: center;
        }
        
        .rule-btn {
            width: 20px;
            height: 20px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            color: #6c757d;
            font-size: 12px;
            font-weight: 500;
            background: #fff;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            flex-shrink: 0;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .rule-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .rule-btn.delete-btn {
            background: #fff5f5;
            color: #dc3545;
            border-color: #f8d7da;
        }

        .rule-btn.delete-btn:hover {
            background: #dc3545;
            color: #fff;
            border-color: #dc3545;
        }

        .rule-btn.edit-btn {
            background: #fff8e1;
            color: #ff9800;
            border-color: #ffecb3;
        }

        .rule-btn.edit-btn:hover {
            background: #ff9800;
            color: #fff;
            border-color: #ff9800;
        }

        .rule-btn.add-btn {
            background: #f0f9ff;
            color: var(--primary-color);
            border-color: #bfdbfe;
            font-size: 14px;
            font-weight: 600;
        }

        .rule-btn.add-btn:hover {
            background: var(--primary-color);
            color: #fff;
            border-color: var(--primary-color);
        }
        
        .edit-row {
            position: relative;
            display: flex;
            align-items: center;
            margin-top: 0;
            gap: 0;
        }
        
        .rule-input {
            width: 100%;
            padding-right: 38px;
        }
        
        ::-webkit-scrollbar {
            width: 7px;
            background: #e9eef6;
            border-radius: 8px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c3cfe2;
            border-radius: 8px;
        }
        
        .footer {
            font-size: 11px;
            text-align: center;
            color: #6c757d;
            margin-top: 24px;
            padding-top: 12px;
            border-top: 1px solid rgba(0,0,0,0.05);
        }
        
        .rule-actions {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .bulk-actions {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .bulk-actions .rule-btn {
            font-size: 10px;
            width: 28px;
            height: 18px;
            padding: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="icons/icon48.png" alt="Logo" class="logo">
            <h1 class="title">aTool</h1>
            <div class="proxy-toggle">
                <label class="switch">
                    <input type="checkbox" id="proxySwitch">
                    <span class="slider"></span>
                </label>
            </div>
        </div>
        
        <div class="status-panel">
            <div class="status-row">
                <span class="status-label">目标主机</span>
                <input type="text" id="targetHost" class="rule-input" value="http://127.0.0.1:9999" style="width: 60%;">
            </div>

        </div>
        
        <div class="rule-list">
            <div class="rule-header">
                <span>规则列表</span>
                <span id="ruleCount">0 条规则</span>
            </div>
            <div class="rules-container">
                <div class="rules-list" id="rulesList">
                    <!-- 规则项将在这里动态添加，每项结构为.input-row -->
                </div>
                <div class="input-row">
                    <input type="text" class="rule-input" placeholder="输入新规则" id="newRuleInput">
                    <button class="rule-btn add-btn" title="添加">+</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>