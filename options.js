document.addEventListener('DOMContentLoaded', () => {
    const globalEnableSwitch = document.getElementById('globalEnableSwitch');
    const targetHostInput = document.getElementById('targetHostInput');
    const statusMessage = document.getElementById('statusMessage');
    const globalStatusMessage = document.getElementById('globalStatusMessage');
    const apiRulesContainer = document.getElementById('apiRulesContainer');
    const addRuleBtn = document.getElementById('addRuleBtn');
    const newRuleInput = document.getElementById('newRuleInput');

    let settings = {
        globalEnable: false,
        targetHost: '',
        pathRules: [],
        pathRulesEnabled: []
    };

    // 全局提示
    let globalStatusTimer = null;
    function showGlobalStatus(msg) {
        if (!globalStatusMessage) return;
        globalStatusMessage.textContent = msg;
        globalStatusMessage.classList.add('show');
        clearTimeout(globalStatusTimer);
        globalStatusTimer = setTimeout(() => {
            globalStatusMessage.classList.remove('show');
        }, 2000);
    }

    // 加载配置
    const loadSettings = () => {
        chrome.storage.local.get('settings', (data) => {
            if (data.settings) {
                settings = data.settings;
                settings.pathRules = settings.pathRules || [];
                settings.pathRulesEnabled = settings.pathRulesEnabled || settings.pathRules.map(() => true);
            }
            render();
        });
    };

    // 保存配置
    const saveSettings = (msg) => {
        settings.globalEnable = globalEnableSwitch.checked;
        settings.targetHost = targetHostInput.value.trim();
        chrome.storage.local.set({ settings }, () => {
            chrome.runtime.sendMessage({
                type: 'SETTINGS_UPDATED',
                payload: settings
            });
            if (msg) showGlobalStatus(msg);
        });
    };

    // 渲染UI
    const render = () => {
        globalEnableSwitch.checked = settings.globalEnable || false;
        targetHostInput.value = settings.targetHost || '';
        apiRulesContainer.innerHTML = '';
        (settings.pathRules || []).forEach((pattern, idx) => {
            addRuleElement(pattern, idx);
        });
    };

    // 添加规则元素（只渲染已添加的规则项）
    const addRuleElement = (pattern = '', idx = -1) => {
        const ruleEl = document.createElement('div');
        ruleEl.className = 'api-rule';
        if (settings.pathRulesEnabled[idx] === false) {
            ruleEl.classList.add('disabled');
        }
        ruleEl.innerHTML = `
            <input type="text" class="api-pattern" value="${pattern}" readonly>
            <button class="remove-rule-btn icon-btn">-</button>
        `;
        apiRulesContainer.appendChild(ruleEl);
        // 删除事件
        const removeBtn = ruleEl.querySelector('.remove-rule-btn');
        removeBtn.addEventListener('click', () => {
            const idx = settings.pathRules.indexOf(pattern);
            if (idx > -1) {
                settings.pathRules.splice(idx, 1);
                settings.pathRulesEnabled.splice(idx, 1);
                saveSettings('规则已删除');
                render();
            }
        });
        // 双击启用/停用
        ruleEl.addEventListener('dblclick', () => {
            if (idx > -1) {
                settings.pathRulesEnabled[idx] = !settings.pathRulesEnabled[idx];
                saveSettings(settings.pathRulesEnabled[idx] ? '规则已启用' : '规则已停用');
                render();
            }
        });
    };

    // --- 事件监听 ---
    globalEnableSwitch.addEventListener('change', () => {
        saveSettings(globalEnableSwitch.checked ? '代理已启用' : '代理已停用');
    });
    targetHostInput.addEventListener('input', () => {
        saveSettings('目标主机已更新');
    });
    addRuleBtn.addEventListener('click', () => {
        const pattern = newRuleInput.value.trim();
        if (!pattern) {
            showGlobalStatus('请输入规则');
            return;
        }
        // 简单格式校验（可根据实际需求调整）
        if (!/^\/.+|https?:\/\/.+/.test(pattern)) {
            showGlobalStatus('规则格式不正确');
            return;
        }
        if (settings.pathRules.includes(pattern)) {
            showGlobalStatus('规则已存在');
            return;
        }
        settings.pathRules.push(pattern);
        settings.pathRulesEnabled.push(true);
        saveSettings('规则已添加');
        render();
        newRuleInput.value = '';
    });

    // 自动关闭窗口：失去焦点或页面不可见时关闭
    window.addEventListener('blur', () => {
      window.close();
    });
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        window.close();
      }
    });

    // 初始化
    loadSettings();
});