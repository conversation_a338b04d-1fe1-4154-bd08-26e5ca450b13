// 拦截器脚本 - 在页面上下文中运行，拦截fetch请求
(function() {
    'use strict';

    // 保存原始的fetch函数
    const originalFetch = window.fetch;

    // 重写fetch函数
    window.fetch = async function(input, init = {}) {
        try {
            // 构建请求配置
            const config = {
                method: init.method || 'GET',
                headers: init.headers || {},
                body: init.body || null
            };

            // 获取URL
            let url;
            if (typeof input === 'string') {
                url = input;
            } else if (input instanceof Request) {
                url = input.url;
                config.method = input.method;
                config.headers = Object.fromEntries(input.headers.entries());
                if (input.body) {
                    config.body = await input.text();
                }
            } else {
                url = String(input);
            }

            // 向content script发送消息，询问是否需要代理
            const response = await new Promise((resolve) => {
                window.postMessage({
                    type: 'PROXY_FETCH_REQUEST',
                    payload: { url, config }
                }, '*');

                // 监听响应
                const messageHandler = (event) => {
                    if (event.source === window && 
                        event.data && 
                        event.data.type === 'PROXY_FETCH_RESPONSE') {
                        window.removeEventListener('message', messageHandler);
                        resolve(event.data);
                    }
                };
                window.addEventListener('message', messageHandler);

                // 超时处理
                setTimeout(() => {
                    window.removeEventListener('message', messageHandler);
                    resolve({ action: 'PROCEED_ORIGINAL' });
                }, 5000);
            });

            // 根据响应决定如何处理
            if (response.action === 'PROXY_RESPONSE') {
                // 返回代理的响应
                const proxyResponse = new Response(response.data.body, {
                    status: response.data.status,
                    statusText: response.data.statusText,
                    headers: response.data.headers
                });
                
                // 显示代理成功提示
                window.postMessage({ type: 'PROXY_SUCCESS_TIP' }, '*');
                
                return proxyResponse;
            } else if (response.error) {
                console.error('代理请求失败:', response.details);
                // 发生错误时，继续使用原始请求
                return originalFetch.apply(this, arguments);
            } else {
                // 不需要代理，使用原始请求
                return originalFetch.apply(this, arguments);
            }
        } catch (error) {
            console.error('拦截器处理错误:', error);
            // 出错时回退到原始fetch
            return originalFetch.apply(this, arguments);
        }
    };

    // 保持原始fetch的属性
    Object.defineProperty(window.fetch, 'name', { value: 'fetch' });
    Object.defineProperty(window.fetch, 'toString', { 
        value: () => 'function fetch() { [native code] }' 
    });

    console.log('开发代理：fetch拦截器已注入');
})();
