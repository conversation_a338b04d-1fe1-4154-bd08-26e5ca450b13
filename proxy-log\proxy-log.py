from fastapi import FastAP<PERSON>, Request
import httpx
import logging
import json
from datetime import datetime
from fastapi.responses import JSONResponse

app = FastAPI()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('proxy.log')
    ]
)

@app.api_route("/{path:path}", methods=["OPTIONS", "GET", "POST", "PUT", "DELETE", "PATCH"])
async def proxy_request(path: str, request: Request):
    # 记录请求信息
    request_info = {
        # "timestamp": datetime.now().isoformat(),
        "method": request.method,
        "path": path,
        # "headers": dict(request.headers),
        # "query_params": dict(request.query_params),
        # "client": request.client.host if request.client else None
    }
    
    try:
        # 读取请求体
        body = await request.body()
        if body:
            request_info["body"] = body.decode()
        
        logging.info("Received request:\n" + json.dumps(request_info, indent=2, ensure_ascii=False))
        
        # 构建目标URL (使用X-Original-URL头)
        target_url = request.headers.get('X-Original-URL')
        if not target_url:
            raise ValueError("Missing X-Original-URL header")
        
        # 转发请求
        async with httpx.AsyncClient() as client:
            # 保留原始请求方法、headers和body
            response = await client.request(
                request.method,
                target_url,
                headers=dict(request.headers),
                params=request.query_params,
                content=body
            )
            
            # 记录响应信息
            response_info = {
                "timestamp": datetime.now().isoformat(),
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content": response.text
            }
            logging.info("Response:\n" + json.dumps(response_info, indent=2, ensure_ascii=False))
            
            # 返回响应
            return JSONResponse(
                content=response.json(),
                status_code=response.status_code,
                headers=dict(response.headers)
            )
            
    except Exception as e:
        error_info = {
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "request": request_info
        }
        logging.error("Error processing request:\n" + json.dumps(error_info, indent=2, ensure_ascii=False))
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="localhost", port=9999)