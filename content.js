(async () => {
  // 从storage中读取配置
  const { settings } = await chrome.storage.local.get('settings');
  // 如果全局开关开启，则显示提示
  if (settings && settings.globalEnable) {
    const indicator = document.createElement('div');
    indicator.textContent = '开发代理已启用';
    // 设置样式
    indicator.style.cssText = `
      position: fixed; top: 15px; right: 15px; 
      background-color: #ff5722; /* 更改为橙色背景 */
      color: white;
      padding: 8px 15px; 
      border-radius: 5px; 
      font-size: 14px; 
      font-weight: bold; /* 加粗字体 */
      z-index: 99999;
      box-shadow: 0 2px 15px rgba(255,87,34,0.3); /* 更明显的阴影 */
      border: 1px solid #e64a19; /* 添加边框 */
      transition: opacity 0.5s ease-in-out, transform 0.3s ease;
      transform: translateY(-10px); /* 初始位置偏上 */
      animation: pulse 2s infinite; /* 添加脉动效果 */
    `;
    
    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
      @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(255,87,34,0.4); }
        70% { box-shadow: 0 0 0 10px rgba(255,87,34,0); }
        100% { box-shadow: 0 0 0 0 rgba(255,87,34,0); }
      }
    `;
    document.head.appendChild(style);
    // 延时显示，避免页面加载初期的闪烁
    setTimeout(() => {
        document.body.appendChild(indicator);
    }, 500);
    
    // 3秒后自动淡出并移除
    setTimeout(() => {
      indicator.style.opacity = '0';
      setTimeout(() => indicator.remove(), 500);
    }, 3500);
  }
})();

const s = document.createElement('script');
s.src = chrome.runtime.getURL('interceptor.js');
document.documentElement.appendChild(s);
// 脚本加载后立即移除DOM节点，保持页面整洁
s.onload = () => s.remove();

// 注入脚本到页面上下文，读取多个源的Authorization
const script = document.createElement('script');
script.textContent = `
  (function() {
    function extractAuthorization() {
      let authorization = null;
      
      try {
        // 方法1: 读取localStorage['APP_STATE_PERSISTENT']
        const persistentState = localStorage.getItem('APP_STATE_PERSISTENT');
        if (persistentState) {
          const obj = JSON.parse(persistentState);
          authorization = obj.Authorization || obj.authorization || obj.token || null;
        }
        
        // 方法2: 读取其他常见的localStorage项
        if (!authorization) {
          const commonKeys = ['auth_token', 'access_token', 'jwt_token', 'user_token', 'authToken'];
          for (const key of commonKeys) {
            const value = localStorage.getItem(key);
            if (value) {
              try {
                const parsed = JSON.parse(value);
                authorization = parsed.token || parsed.access_token || parsed.authorization || value;
                if (authorization) break;
              } catch {
                // 如果不是JSON，直接使用值
                authorization = value;
                break;
              }
            }
          }
        }
        
        // 方法3: 检查sessionStorage
        if (!authorization) {
          const sessionAuth = sessionStorage.getItem('authorization') || sessionStorage.getItem('auth_token');
          if (sessionAuth) {
            authorization = sessionAuth;
          }
        }
        
      } catch (e) {
        console.warn('提取Authorization时出错:', e);
      }
      
      return authorization;
    }
    
    // 立即提取一次
    const authorization = extractAuthorization();
    window.postMessage({ type: 'PROXY_EXT_AUTH', authorization }, '*');
    
    // 监听storage变化，实时更新
    const storageHandler = () => {
      const newAuth = extractAuthorization();
      window.postMessage({ type: 'PROXY_EXT_AUTH', authorization: newAuth }, '*');
    };
    
    window.addEventListener('storage', storageHandler);
    
    // 定期检查更新（防止遗漏）
    setInterval(storageHandler, 30000); // 每30秒检查一次
  })();
`;
(document.head || document.documentElement).appendChild(script);
script.remove();

// 监听window消息，处理各种类型的消息
window.addEventListener('message', function(event) {
  if (event.source !== window) return;
  
  if (event.data && event.data.type === 'PROXY_EXT_AUTH') {
    // 将Authorization转发给background.js
    chrome.runtime.sendMessage({ type: 'PROXY_EXT_AUTH', authorization: event.data.authorization });
  } else if (event.data && event.data.type === 'PROXY_FETCH_REQUEST') {
    // 处理fetch拦截请求
    chrome.runtime.sendMessage({
      type: 'PROXY_FETCH',
      payload: event.data.payload
    }, (response) => {
      // 将响应发送回页面
      window.postMessage({
        type: 'PROXY_FETCH_RESPONSE',
        ...response
      }, '*');
    });
  }
});

window.addEventListener('message', function(event) {
  if (event.data && event.data.type === 'PROXY_SUCCESS_TIP') {
    // 创建tip元素
    const tip = document.createElement('div');
    tip.textContent = 'API代理成功';
    tip.style.cssText = `
      position: fixed; top: 15px; right: 15px;
      background-color: #4caf50;
      color: white;
      padding: 8px 15px;
      border-radius: 5px;
      font-size: 14px;
      font-weight: bold;
      z-index: 99999;
      box-shadow: 0 2px 15px rgba(76,175,80,0.3);
      border: 1px solid #388e3c;
      transition: opacity 0.5s ease-in-out, transform 0.3s ease;
      transform: translateY(-10px);
      animation: pulseTip 2s infinite;
    `;
    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
      @keyframes pulseTip {
        0% { box-shadow: 0 0 0 0 rgba(76,175,80,0.4); }
        70% { box-shadow: 0 0 0 10px rgba(76,175,80,0); }
        100% { box-shadow: 0 0 0 0 rgba(76,175,80,0); }
      }
    `;
    document.head.appendChild(style);
    document.body.appendChild(tip);
    setTimeout(() => {
      tip.style.opacity = '0';
      setTimeout(() => tip.remove(), 500);
    }, 3000);
  }
});
