/* 全局样式 */
html, body {
  min-width: 400px;
  width: 400px;
  max-width: 400px;
  min-height: 250px;
  height: 250px;
  max-height: 300px;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  background: #181c24;
  color: #e3e6eb;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  /* Hide scrollbar but keep scrolling enabled */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* Hide scrollbar for Webkit browsers */
html::-webkit-scrollbar,
body::-webkit-scrollbar,
.container::-webkit-scrollbar {
  display: none;
}
.container {
  width: 380px;
  box-sizing: border-box;
  position: relative;
  background: #232837;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(74,131,255,0.08);
  padding: 12px 10px 8px 10px;
  color: #e3e6eb;
  margin: 5px;
  max-height: calc(100vh - 10px);
  overflow-y: auto;
  overflow-x: hidden;
  /* Hide scrollbar but keep scrolling enabled */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
.top-buttons {
  width: 100%;
  height: 0;
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none; /* 让按钮不影响布局，只响应自身事件 */
}
header {
  text-align: center;
  margin-bottom: 18px;
}
.logo {
  width: 48px;
  height: 48px;
  border-radius: 12px;
}
h1 {
  margin: 8px 0 0 0;
  color: #4a83ff;
  font-size: 1.6em;
}
.subtitle {
  color: #888;
  font-size: 1em;
}
.card {
  background: #232b3b;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 14px;
  box-shadow: 0 1px 4px rgba(74,131,255,0.08);
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.card.actions {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background: none;
  box-shadow: none;
  padding: 0;
}
input[type="text"], textarea {
  background: #232837;
  color: #e3e6eb;
  border: 1px solid #3a4256;
  border-radius: 4px;
  padding: 8px;
  font-size: 0.9em;
  width: 100%;
  box-sizing: border-box;
  transition: border 0.2s;
}
input[type="text"]:focus, textarea:focus {
  border-color: #4a83ff;
  outline: none;
}
/* 按钮统一风格 */
button,
.button {
  background: #4a83ff;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 6px 16px;
  font-size: 0.9em;
  margin: 0 8px 0 0;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 1px 4px rgba(74,131,255,0.08);
  display: inline-block;
  min-width: 80px;
}
button:last-child,
.button:last-child {
  margin-right: 0;
}
button:hover,
.button:hover {
  background: #3566cc;
}
.button-group {
  display: flex;
  gap: 18px;
  margin-top: 18px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}
/* 删除按钮的特殊样式，统一主色调 */
button.danger,
.button.danger {
  background: #4a83ff;
  color: #fff;
  border: none;
}
button.danger:hover,
.button.danger:hover {
  background: #3566cc;
}
/* 统一icon按钮样式 - 加号和减号按钮相同大小 */
.icon-btn {
  width: 36px;
  height: 36px;
  min-width: 36px;
  min-height: 36px;
  padding: 0;
  font-size: 1.4em;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #4a83ff;
  color: #fff;
  border: none;
  margin-left: 8px;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(74,131,255,0.08);
}
.icon-btn:hover {
  background: #3566cc;
}
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.slider {
  position: absolute;
  cursor: pointer;
  top: 0; left: 0; right: 0; bottom: 0;
  background-color: #444b5a;
  border-radius: 24px;
  transition: .4s;
}
.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  border-radius: 50%;
  transition: .4s;
}
input:checked + .slider {
  background-color: #4a83ff;
}
input:checked + .slider:before {
  transform: translateX(20px);
}
.label {
  margin-left: 12px;
  font-size: 1.1em;
  color: #e3e6eb;
  align-self: center;
}
.message {
  margin-top: 10px;
  color: #4a83ff;
  font-size: 1em;
  min-height: 1.2em;
}
footer {
  text-align: center;
  margin-top: 18px;
  color: #888;
  font-size: 0.95em;
}
.cache-clear-btn {
  position: absolute;
  top: clamp(8px, 2vw, 20px);
  right: 20px;
  z-index: 10;
  background: #4a83ff;
  color: #fff;
  border-radius: 6px;
  padding: 8px 18px;
  font-size: 1em;
  box-shadow: 0 2px 8px rgba(74,131,255,0.08);
  transition: background 0.2s;
}
.cache-clear-btn:hover {
  background: #3566cc;
}
.top-left-switch {
  position: absolute;
  top: clamp(8px, 2vw, 20px);
  left: 20px;
  z-index: 10;
}
.api-rule {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}
#apiRulesContainer .api-rule {
  margin-bottom: 8px;
}
#apiRulesContainer:empty {
  display: none;
}
.top-right-switch {
  position: absolute;
  top: 12px;
  right: 18px;
  z-index: 10;
  pointer-events: auto;
}
.api-rule.disabled {
  opacity: 0.5;
  filter: grayscale(0.7);
  text-decoration: line-through;
}
.api-rule.disabled input {
  background: #2a2d36;
  color: #888;
  text-decoration: line-through;
}
.global-status-message {
  position: absolute;
  top: 2px;
  left: 50%;
  transform: translateX(-50%);
  background: none;
  color: #e6b800;
  padding: 8px 28px;
  border-radius: 8px;
  font-size: 1em;
  font-weight: bold;
  box-shadow: none;
  z-index: 100;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
}
.global-status-message.show {
  opacity: 1;
  pointer-events: auto;
}
.section {
  margin-top: 0;
  margin-bottom: 8px;
}
.section:last-of-type {
  margin-bottom: 0;
}
h2, h3 {
  font-size: 1.0em;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 4px;
}
@media (max-width: 500px) {
  .container { max-width: 98vw; padding: 18px 2vw 10px 2vw; }
  .card { padding: 10px; }
  .top-left-switch, .cache-clear-btn {
    top: 8px;
    padding: 6px 10px;
    font-size: 0.95em;
  }
  .top-right-switch {
    top: 8px;
  }
}
