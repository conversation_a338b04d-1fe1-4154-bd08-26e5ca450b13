{"manifest_version": 3, "name": ".", "version": "2.1.0", "description": "拦截API调用，将其转发到指定主机，并缓存Authorization头。", "permissions": ["storage", "scripting", "activeTab"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "action": {"default_title": "", "default_popup": "options.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "options_page": "options.html", "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_start"}], "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["interceptor.js"], "matches": ["<all_urls>"]}]}